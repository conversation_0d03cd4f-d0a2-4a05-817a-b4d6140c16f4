import request from '@/utils/request'

// 查询点位锁列表
export function listPointLock(query) {
  return request({
    url: '/biz/pointLock/list',
    method: 'get',
    params: query
  })
}

// 查询点位锁详细
export function getPointLock(id) {
  return request({
    url: '/biz/pointLock/' + id,
    method: 'get'
  })
}

// 新增点位锁
export function addPointLock(data) {
  return request({
    url: '/biz/pointLock',
    method: 'post',
    data: data
  })
}

// 修改点位锁
export function updatePointLock(data) {
  return request({
    url: '/biz/pointLock',
    method: 'put',
    data: data
  })
}

// 删除点位锁
export function delPointLock(id) {
  return request({
    url: '/biz/pointLock/' + id,
    method: 'delete'
  })
}
