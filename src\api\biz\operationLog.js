import request from '@/utils/request'

// 查询操作日志列表
export function listOperationLog(query) {
  return request({
    url: '/biz/operationLog/list',
    method: 'get',
    params: query
  })
}

// 查询操作日志详细
export function getOperationLog(id) {
  return request({
    url: '/biz/operationLog/' + id,
    method: 'get'
  })
}

// 新增操作日志
export function addOperationLog(data) {
  return request({
    url: '/biz/operationLog',
    method: 'post',
    data: data
  })
}

// 修改操作日志
export function updateOperationLog(data) {
  return request({
    url: '/biz/operationLog',
    method: 'put',
    data: data
  })
}

// 删除操作日志
export function delOperationLog(id) {
  return request({
    url: '/biz/operationLog/' + id,
    method: 'delete'
  })
}
