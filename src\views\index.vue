<template>
  <div class="app-container home">
    <!-- 项目信息 - 顶部 -->
    <div class="top-project-info">
      <div class="content-card project-info-card">
        <div class="card-header">
          <h3><i class="el-icon-office-building"></i> 项目信息</h3>
        </div>
        <div class="project-info-content">
          <div v-if="projectLoading" class="project-loading">
            <i class="el-icon-loading"></i>
            <span>正在加载项目信息...</span>
          </div>
          <div v-else class="project-details">
            <!-- 项目标题区域 -->
            <div class="project-header">
              <div class="project-icon">
                <i class="el-icon-office-building"></i>
              </div>
              <div class="project-title">
                <h3>{{ projectInfo.name || '未知项目' }}</h3>
                <p class="project-dept">{{ projectInfo.deptName || '未知单位' }}</p>
              </div>
            </div>

            <!-- 项目详细信息 -->
            <div class="project-info-grid">
              <div class="info-item">
                <div class="info-icon">
                  <i class="el-icon-location-outline"></i>
                </div>
                <div class="info-content">
                  <div class="info-label">项目地址</div>
                  <div class="info-value">{{ projectInfo.address || '暂无地址信息' }}</div>
                </div>
              </div>

              <div class="info-item">
                <div class="info-icon">
                  <i class="el-icon-user"></i>
                </div>
                <div class="info-content">
                  <div class="info-label">联系人</div>
                  <div class="info-value">{{ projectInfo.contacts || '暂无联系人' }}</div>
                </div>
              </div>

              <div class="info-item">
                <div class="info-icon">
                  <i class="el-icon-phone"></i>
                </div>
                <div class="info-content">
                  <div class="info-label">联系电话</div>
                  <div class="info-value">{{ projectInfo.contactsNumber || '暂无电话' }}</div>
                </div>
              </div>

              <div class="info-item" v-if="projectInfo.startTime">
                <div class="info-icon">
                  <i class="el-icon-date"></i>
                </div>
                <div class="info-content">
                  <div class="info-label">开始时间</div>
                  <div class="info-value">{{ projectInfo.startTime }}</div>
                </div>
              </div>

              <div class="info-item" v-if="projectInfo.endTime">
                <div class="info-icon">
                  <i class="el-icon-date"></i>
                </div>
                <div class="info-content">
                  <div class="info-label">结束时间</div>
                  <div class="info-value">{{ projectInfo.endTime }}</div>
                </div>
              </div>
            </div>

            <!-- 项目描述 -->
            <div class="project-description" v-if="projectInfo.detail">
              <div class="description-header">
                <i class="el-icon-document"></i>
                <span>项目概况</span>
              </div>
              <div class="description-content">
                {{ projectInfo.detail }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>



    <!-- 第一行：设备状态和快捷操作 -->
    <el-row :gutter="20" class="first-row">
      <!-- 左侧：设备状态统计 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <div class="content-card device-status">
          <div class="card-header">
            <h3><i class="el-icon-monitor"></i> 设备状态</h3>
          </div>
          <div class="device-stats-grid">
            <div class="device-stat-item device-card">
              <div class="stat-icon">
                <i class="el-icon-cpu"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ statsData.deviceCount || 0 }}</div>
                <div class="stat-label">设备总数</div>
                <div class="stat-status online">
                  <i class="el-icon-success"></i>
                  {{ statsData.onlineDevices || 0 }}台在线
                </div>
              </div>
            </div>

            <div class="device-stat-item meter-card">
              <div class="stat-icon">
                <i class="el-icon-data-line"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ statsData.meterCount || 0 }}</div>
                <div class="stat-label">表具总数</div>
                <div class="stat-status normal">
                  <i class="el-icon-circle-check"></i>
                  {{ statsData.normalMeters || 0 }}台正常
                </div>
              </div>
            </div>

            <div class="device-stat-item alarm-card">
              <div class="stat-icon">
                <i class="el-icon-warning"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ statsData.alarmCount || 0 }}</div>
                <div class="stat-label">告警数量</div>
                <div class="stat-status warning">
                  <i class="el-icon-warning-outline"></i>
                  {{ statsData.unhandledAlarms || 0 }}条未处理
                </div>
              </div>
            </div>

            <div class="device-stat-item project-card">
              <div class="stat-icon">
                <i class="el-icon-office-building"></i>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ statsData.areaCount || 0 }}</div>
                <div class="stat-label">区域数量</div>
                <div class="stat-status info">
                  <i class="el-icon-location"></i>
                  {{ statsData.totalAreas || 0 }}个区域
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-col>

      <!-- 右侧：快捷操作 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <div class="content-card quick-actions">
          <div class="card-header">
            <h3><i class="el-icon-s-grid"></i> 快捷操作</h3>
          </div>
          <div class="actions-grid">
            <div class="action-item" @click="navigateTo('/biz/device/list')">
              <div class="action-icon device-icon">
                <i class="el-icon-cpu"></i>
              </div>
              <div class="action-label">设备管理</div>
            </div>
            <div class="action-item" @click="navigateTo('/biz/meter/list')">
              <div class="action-icon meter-icon">
                <i class="el-icon-data-line"></i>
              </div>
              <div class="action-label">表具管理</div>
            </div>
            <div class="action-item" @click="navigateTo('/biz/area')">
              <div class="action-icon area-icon">
                <i class="el-icon-office-building"></i>
              </div>
              <div class="action-label">区域管理</div>
            </div>
            <div class="action-item" @click="navigateTo('/biz/project')">
              <div class="action-icon project-icon">
                <i class="el-icon-folder-opened"></i>
              </div>
              <div class="action-label">项目管理</div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 第二行：告警信息和操作日志 -->
    <el-row :gutter="20" class="second-row">
      <!-- 左侧：告警信息 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <div class="content-card alarms">
          <div class="card-header">
            <h3><i class="el-icon-warning"></i> 告警信息</h3>
            <div class="header-actions">
              <el-button size="mini" type="text" @click="viewAllAlarms">
                查看全部
              </el-button>
            </div>
          </div>
          <div class="alarm-list">
            <div
              v-for="(alarm, index) in alarms"
              :key="index"
              class="alarm-item"
              :class="alarm.level"
            >
              <div class="alarm-icon">
                <i :class="getAlarmIcon(alarm.level)"></i>
              </div>
              <div class="alarm-content">
                <div class="alarm-title">{{ alarm.title }}</div>
                <div class="alarm-device">{{ alarm.deviceName }}</div>
                <div class="alarm-time">{{ alarm.time }}</div>
              </div>
              <div class="alarm-status" :class="alarm.status">
                {{ getAlarmStatusText(alarm.status) }}
              </div>
            </div>
            <div v-if="alarms.length === 0" class="no-alarms">
              <i class="el-icon-success"></i>
              <span>暂无告警信息</span>
            </div>
          </div>
        </div>
      </el-col>

      <!-- 右侧：操作日志 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <div class="content-card operation-logs">
          <div class="card-header">
            <h3><i class="el-icon-document"></i> 操作日志</h3>
            <div class="header-actions">
              <el-button size="mini" type="text" @click="viewAllLogs">
                查看全部
              </el-button>
            </div>
          </div>
          <div class="log-list">
            <div
              v-for="(log, index) in operationLogs"
              :key="index"
              class="log-item"
            >
              <div class="log-icon">
                <i :class="getLogIcon(log.type)"></i>
              </div>
              <div class="log-content">
                <div class="log-title">{{ log.title }}</div>
                <div class="log-user">{{ log.userName }}</div>
                <div class="log-time">{{ log.time }}</div>
              </div>
            </div>
            <div v-if="operationLogs.length === 0" class="no-logs">
              <i class="el-icon-info"></i>
              <span>暂无操作记录</span>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import { getProject } from '@/api/biz/project'

export default {
  name: "Index",
  data() {
    return {
      // 统计数据
      statsData: {
        deviceCount: 0,
        onlineDevices: 0,
        meterCount: 0,
        normalMeters: 0,
        alarmCount: 0,
        unhandledAlarms: 0,
        areaCount: 0,
        totalAreas: 0
      },

      // 项目信息
      projectInfo: {
        name: '',
        deptName: '',
        address: '',
        contacts: '',
        contactsNumber: '',
        detail: '',
        startTime: '',
        endTime: ''
      },

      // 项目加载状态
      projectLoading: false,



      // 告警列表
      alarms: [],

      // 操作日志
      operationLogs: [],

      // 定时器
      refreshTimer: null
    };
  },

  mounted() {
    this.initPage();
    this.startAutoRefresh();
  },

  beforeDestroy() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
    }
  },

  methods: {
    // 初始化页面数据
    async initPage() {
      await this.loadProjectInfo();
      await this.loadStatsData();
      await this.loadAlarms();
      await this.loadOperationLogs();
    },

    // 加载项目信息
    async loadProjectInfo() {
      this.projectLoading = true;

      try {
        // 从localStorage获取项目ID
        const projectId = localStorage.getItem('projectId');

        if (!projectId) {
          console.warn('未找到项目ID，请先选择项目');
          this.projectInfo = {
            name: '请先选择项目',
            deptName: '-',
            address: '-',
            contacts: '-',
            contactsNumber: '-',
            detail: '请在项目选择页面选择一个项目后再查看详细信息。',
            startTime: '',
            endTime: ''
          };
          return;
        }

        // 调用项目详情API
        const response = await getProject(projectId);

        if (response && response.data) {
          this.projectInfo = {
            name: response.data.name || '未知项目',
            deptName: response.data.deptName || '未知单位',
            address: response.data.address || '未知地址',
            contacts: response.data.contacts || '未知联系人',
            contactsNumber: response.data.contactsNumber || '未知电话',
            detail: response.data.detail || '暂无项目描述信息。',
            startTime: response.data.startTime ? this.parseTime(response.data.startTime, '{y}-{m}-{d}') : '',
            endTime: response.data.endTime ? this.parseTime(response.data.endTime, '{y}-{m}-{d}') : ''
          };

          console.log('项目信息加载成功:', this.projectInfo);
        } else {
          throw new Error('项目信息获取失败');
        }
      } catch (error) {
        console.error('加载项目信息失败:', error);

        // 根据错误类型显示不同的错误信息
        let errorMessage = '加载项目信息失败';
        if (error.response) {
          if (error.response.status === 404) {
            errorMessage = '项目不存在或已被删除';
          } else if (error.response.status === 403) {
            errorMessage = '没有权限访问该项目';
          } else {
            errorMessage = error.response.data?.msg || '服务器错误';
          }
        } else if (error.message) {
          errorMessage = error.message;
        }

        this.$modal.msgError(errorMessage);

        // 设置默认错误信息
        this.projectInfo = {
          name: '项目信息加载失败',
          deptName: '加载失败',
          address: '加载失败',
          contacts: '加载失败',
          contactsNumber: '加载失败',
          detail: '无法获取项目详细信息，请检查网络连接或联系管理员。',
          startTime: '',
          endTime: ''
        };
      } finally {
        this.projectLoading = false;
      }
    },

    // 加载统计数据
    async loadStatsData() {
      try {
        // 模拟API调用，实际应该调用后端接口
        // const response = await this.$http.get('/api/dashboard/stats');

        // 模拟数据
        this.statsData = {
          deviceCount: 156,
          onlineDevices: 142,
          meterCount: 89,
          normalMeters: 85,
          alarmCount: 12,
          unhandledAlarms: 3,
          areaCount: 8,
          totalAreas: 8
        };
      } catch (error) {
        console.error('加载统计数据失败:', error);
        this.$modal.msgError('加载统计数据失败');
      }
    },



    // 加载告警数据
    async loadAlarms() {
      try {
        // 模拟告警数据
        this.alarms = [
          {
            level: 'high',
            title: '温度超限告警',
            deviceName: '空调设备A01',
            time: '2分钟前',
            status: 'unhandled'
          },
          {
            level: 'medium',
            title: '设备离线告警',
            deviceName: '服务器B02',
            time: '15分钟前',
            status: 'handling'
          },
          {
            level: 'low',
            title: '电压波动告警',
            deviceName: 'UPS设备C01',
            time: '1小时前',
            status: 'handled'
          }
        ];
      } catch (error) {
        console.error('加载告警数据失败:', error);
      }
    },

    // 加载操作日志
    async loadOperationLogs() {
      try {
        // 模拟操作日志数据
        this.operationLogs = [
          {
            type: 'control',
            title: '设备控制操作',
            userName: '管理员',
            time: '10分钟前'
          },
          {
            type: 'config',
            title: '系统配置修改',
            userName: '张工程师',
            time: '30分钟前'
          },
          {
            type: 'login',
            title: '用户登录',
            userName: '李操作员',
            time: '1小时前'
          },
          {
            type: 'maintenance',
            title: '设备维护记录',
            userName: '维护人员',
            time: '2小时前'
          }
        ];
      } catch (error) {
        console.error('加载操作日志失败:', error);
      }
    },

    // 获取告警图标
    getAlarmIcon(level) {
      const iconMap = {
        high: 'el-icon-error',
        medium: 'el-icon-warning',
        low: 'el-icon-info'
      };
      return iconMap[level] || 'el-icon-info';
    },

    // 获取告警状态文本
    getAlarmStatusText(status) {
      const statusMap = {
        unhandled: '未处理',
        handling: '处理中',
        handled: '已处理'
      };
      return statusMap[status] || '未知';
    },

    // 获取操作日志图标
    getLogIcon(type) {
      const iconMap = {
        control: 'el-icon-setting',
        config: 'el-icon-tools',
        login: 'el-icon-user',
        maintenance: 'el-icon-service'
      };
      return iconMap[type] || 'el-icon-document';
    },



    // 导航到指定页面
    navigateTo(path) {
      this.$router.push(path);
    },



    // 查看所有告警
    viewAllAlarms() {
      this.$message.info('跳转到告警管理页面');
      // 这里可以跳转到告警管理页面
    },

    // 查看所有日志
    viewAllLogs() {
      this.$message.info('跳转到操作日志页面');
      // 这里可以跳转到操作日志页面
    },

    // 开始自动刷新
    startAutoRefresh() {
      this.refreshTimer = setInterval(() => {
        this.loadAlarms();
        this.loadOperationLogs();
      }, 30000); // 每30秒刷新一次数据
    }
  }
};
</script>
<style scoped lang="scss">
.home {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 84px);

  /* 顶部项目信息 */
  .top-project-info {
    margin-bottom: 20px;

    .content-card {
      background: white;
      border-radius: 12px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      border: 1px solid #ebeef5;
      overflow: hidden;

      .card-header {
        padding: 20px 24px;
        border-bottom: 1px solid #ebeef5;
        background: #fafbfc;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          display: flex;
          align-items: center;
          gap: 8px;

          i {
            color: #409eff;
          }
        }
      }

      /* 项目信息内容样式 */
      &.project-info-card {
        .project-info-content {
          padding: 24px;
          flex: 1;

          .project-loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 200px;
            color: #909399;

            i {
              font-size: 32px;
              margin-bottom: 12px;
              animation: rotate 2s linear infinite;
            }

            span {
              font-size: 14px;
            }
          }

          .project-details {
            /* 项目标题区域 */
            .project-header {
              display: flex;
              align-items: center;
              gap: 16px;
              margin-bottom: 24px;
              padding: 20px;
              background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
              border-radius: 12px;
              border-left: 4px solid #409eff;

              .project-icon {
                width: 60px;
                height: 60px;
                background: linear-gradient(135deg, #409eff, #36a3f7);
                border-radius: 12px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);

                i {
                  font-size: 28px;
                }
              }

              .project-title {
                flex: 1;

                h3 {
                  margin: 0 0 8px 0;
                  font-size: 20px;
                  font-weight: 600;
                  color: #303133;
                  line-height: 1.2;
                }

                .project-dept {
                  margin: 0;
                  font-size: 14px;
                  color: #606266;
                  display: flex;
                  align-items: center;
                  gap: 4px;

                  &::before {
                    content: '';
                    width: 4px;
                    height: 4px;
                    background: #409eff;
                    border-radius: 50%;
                  }
                }
              }
            }

            /* 项目信息网格 */
            .project-info-grid {
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
              gap: 16px;
              margin-bottom: 24px;

              .info-item {
                display: flex;
                align-items: flex-start;
                gap: 12px;
                padding: 16px;
                background: #fafbfc;
                border-radius: 8px;
                border: 1px solid #ebeef5;
                transition: all 0.3s ease;

                &:hover {
                  background: #f0f9ff;
                  border-color: #409eff;
                  transform: translateY(-1px);
                  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
                }

                .info-icon {
                  width: 36px;
                  height: 36px;
                  background: linear-gradient(135deg, #409eff, #36a3f7);
                  border-radius: 8px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  color: white;
                  flex-shrink: 0;

                  i {
                    font-size: 16px;
                  }
                }

                .info-content {
                  flex: 1;
                  min-width: 0;

                  .info-label {
                    font-size: 12px;
                    color: #909399;
                    margin-bottom: 4px;
                    font-weight: 500;
                  }

                  .info-value {
                    font-size: 14px;
                    color: #303133;
                    font-weight: 500;
                    line-height: 1.4;
                    word-break: break-all;
                  }
                }
              }
            }

            /* 项目描述 */
            .project-description {
              .description-header {
                display: flex;
                align-items: center;
                gap: 8px;
                margin-bottom: 12px;
                font-size: 14px;
                font-weight: 600;
                color: #606266;

                i {
                  color: #409eff;
                  font-size: 16px;
                }
              }

              .description-content {
                padding: 16px;
                background: #fafbfc;
                border-radius: 8px;
                border-left: 4px solid #409eff;
                font-size: 14px;
                color: #606266;
                line-height: 1.6;
                text-align: justify;
              }
            }
          }
        }
      }
    }
  }

  /* 统计卡片 */
  .stats-cards {
    margin-bottom: 20px;

    &.compact .stat-card {
      padding: 16px;

      .stat-icon {
        width: 48px;
        height: 48px;
        margin-bottom: 12px;

        i {
          font-size: 22px;
        }
      }

      .stat-content {
        .stat-number {
          font-size: 24px;
          margin-bottom: 6px;
        }

        .stat-label {
          font-size: 13px;
          margin-bottom: 8px;
        }

        .stat-status {
          font-size: 11px;
        }
      }
    }

    .stat-card {
      background: white;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      border: 1px solid #ebeef5;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
      height: 100%;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      }

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #409eff, #67c23a);
      }

      &.device-card::before {
        background: linear-gradient(90deg, #409eff, #36a3f7);
      }

      &.meter-card::before {
        background: linear-gradient(90deg, #67c23a, #85ce61);
      }

      &.alarm-card::before {
        background: linear-gradient(90deg, #f56c6c, #f78989);
      }

      &.project-card::before {
        background: linear-gradient(90deg, #909399, #b1b3b8);
      }

      .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 16px;
        background: linear-gradient(135deg, #f0f9ff, #e0f2fe);

        i {
          font-size: 28px;
          color: #409eff;
        }
      }

      .stat-content {
        .stat-number {
          font-size: 32px;
          font-weight: 700;
          color: #303133;
          margin-bottom: 8px;
          line-height: 1;
        }

        .stat-label {
          font-size: 14px;
          color: #909399;
          margin-bottom: 12px;
        }

        .stat-status {
          font-size: 12px;
          font-weight: 500;
          display: flex;
          align-items: center;
          gap: 4px;

          &.online {
            color: #67c23a;
          }

          &.normal {
            color: #67c23a;
          }

          &.warning {
            color: #e6a23c;
          }

          &.info {
            color: #409eff;
          }

          i {
            font-size: 12px;
          }
        }
      }
    }
  }

  /* 第一行和第二行布局 */
  .el-row.first-row,
  .el-row.second-row {
    margin-bottom: 20px;

    /* 确保左右两列高度一致 */
    display: flex !important;
    flex-wrap: wrap;
    align-items: stretch;

    .el-col {
      display: flex;
      flex-direction: column;
    }

    .content-card {
      background: white;
      border-radius: 12px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      border: 1px solid #ebeef5;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      flex: 1;
      height: 400px !important; /* 固定高度确保一致 */

      .card-header {
        padding: 20px 24px;
        border-bottom: 1px solid #ebeef5;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: #fafbfc;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          display: flex;
          align-items: center;
          gap: 8px;

          i {
            color: #409eff;
          }
        }

        .header-actions {
          .el-button {
            border: none;
            background: #f4f4f5;
            color: #606266;

            &:hover {
              background: #409eff;
              color: white;
            }
          }
        }
      }





      // 快捷操作样式
      &.quick-actions {
        .actions-grid {
          padding: 24px;
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 16px;
          flex: 1;

          .action-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px 16px;
            border-radius: 8px;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;

            &:hover {
              background: #409eff;
              color: white;
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);

              .action-icon {
                background: rgba(255, 255, 255, 0.2);
                color: white;
              }
            }

            .action-icon {
              width: 48px;
              height: 48px;
              border-radius: 12px;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-bottom: 12px;
              background: white;
              color: #409eff;
              transition: all 0.3s ease;

              i {
                font-size: 24px;
              }

              &.device-icon {
                color: #409eff;
              }

              &.meter-icon {
                color: #67c23a;
              }

              &.analysis-icon {
                color: #e6a23c;
              }

              &.bill-icon {
                color: #f56c6c;
              }

              &.area-icon {
                color: #909399;
              }

              &.project-icon {
                color: #606266;
              }
            }

            .action-label {
              font-size: 14px;
              font-weight: 500;
              text-align: center;
            }
          }
        }
      }

      // 设备状态统计样式
      &.device-status {
        .device-stats-grid {
          padding: 20px;
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 16px;
          flex: 1;

          .device-stat-item {
            background: white;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 1px 6px rgba(0, 0, 0, 0.06);
            border: 1px solid #ebeef5;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;

            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
            }

            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              height: 3px;
              background: linear-gradient(90deg, #409eff, #67c23a);
            }

            &.device-card::before {
              background: linear-gradient(90deg, #409eff, #36a3f7);
            }

            &.meter-card::before {
              background: linear-gradient(90deg, #67c23a, #85ce61);
            }

            &.alarm-card::before {
              background: linear-gradient(90deg, #f56c6c, #f78989);
            }

            &.project-card::before {
              background: linear-gradient(90deg, #909399, #b1b3b8);
            }

            .stat-icon {
              width: 40px;
              height: 40px;
              border-radius: 8px;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-bottom: 12px;
              background: linear-gradient(135deg, #f0f9ff, #e0f2fe);

              i {
                font-size: 20px;
                color: #409eff;
              }
            }

            .stat-content {
              .stat-number {
                font-size: 24px;
                font-weight: 700;
                color: #303133;
                margin-bottom: 6px;
                line-height: 1;
              }

              .stat-label {
                font-size: 12px;
                color: #909399;
                margin-bottom: 8px;
              }

              .stat-status {
                font-size: 10px;
                font-weight: 500;
                display: flex;
                align-items: center;
                gap: 3px;

                &.online {
                  color: #67c23a;
                }

                &.normal {
                  color: #67c23a;
                }

                &.warning {
                  color: #e6a23c;
                }

                &.info {
                  color: #409eff;
                }

                i {
                  font-size: 10px;
                }
              }
            }
          }
        }
      }

      // 告警样式
      &.alarms {
        .alarm-list {
          padding: 24px;
          overflow-y: auto;
          flex: 1;

          .alarm-item {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;

            &:last-child {
              border-bottom: none;
            }

            .alarm-icon {
              width: 32px;
              height: 32px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              flex-shrink: 0;

              i {
                font-size: 16px;
              }
            }

            &.high {
              .alarm-icon {
                background: #fef0f0;
                color: #f56c6c;
              }
            }

            &.medium {
              .alarm-icon {
                background: #fdf6ec;
                color: #e6a23c;
              }
            }

            &.low {
              .alarm-icon {
                background: #f4f4f5;
                color: #909399;
              }
            }

            .alarm-content {
              flex: 1;

              .alarm-title {
                font-size: 14px;
                color: #303133;
                margin-bottom: 4px;
                line-height: 1.4;
              }

              .alarm-device {
                font-size: 12px;
                color: #606266;
                margin-bottom: 2px;
              }

              .alarm-time {
                font-size: 12px;
                color: #c0c4cc;
              }
            }

            .alarm-status {
              font-size: 12px;
              padding: 2px 8px;
              border-radius: 12px;
              font-weight: 500;

              &.unhandled {
                background: #fef0f0;
                color: #f56c6c;
              }

              &.handling {
                background: #fdf6ec;
                color: #e6a23c;
              }

              &.handled {
                background: #f0f9ff;
                color: #67c23a;
              }
            }
          }

          .no-alarms {
            text-align: center;
            padding: 40px 20px;
            color: #c0c4cc;

            i {
              font-size: 48px;
              margin-bottom: 12px;
              display: block;
              color: #67c23a;
            }

            span {
              font-size: 14px;
            }
          }
        }
      }

      // 操作日志样式
      &.operation-logs {
        .log-list {
          padding: 24px;
          overflow-y: auto;
          flex: 1;

          .log-item {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;

            &:last-child {
              border-bottom: none;
            }

            .log-icon {
              width: 32px;
              height: 32px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              flex-shrink: 0;
              background: #f0f9ff;
              color: #409eff;

              i {
                font-size: 16px;
              }
            }

            .log-content {
              flex: 1;

              .log-title {
                font-size: 14px;
                color: #303133;
                margin-bottom: 4px;
                line-height: 1.4;
              }

              .log-user {
                font-size: 12px;
                color: #606266;
                margin-bottom: 2px;
              }

              .log-time {
                font-size: 12px;
                color: #c0c4cc;
              }
            }
          }

          .no-logs {
            text-align: center;
            padding: 40px 20px;
            color: #c0c4cc;

            i {
              font-size: 48px;
              margin-bottom: 12px;
              display: block;
              color: #409eff;
            }

            span {
              font-size: 14px;
            }
          }
        }
      }
    }
  }


}

// 动画效果
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-10px) rotate(180deg);
  }
}

@keyframes pulse {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .home {
    padding: 10px;

    .top-project-info {
      margin-bottom: 16px;

      .content-card .card-header {
        padding: 16px;
      }

      .project-info-content .project-details {
        .project-header {
          flex-direction: column;
          text-align: center;
          gap: 12px;
          padding: 16px;

          .project-icon {
            width: 50px;
            height: 50px;

            i {
              font-size: 24px;
            }
          }

          .project-title h3 {
            font-size: 18px;
          }
        }

        .project-info-grid {
          grid-template-columns: 1fr;
          gap: 12px;

          .info-item {
            padding: 12px;

            .info-icon {
              width: 32px;
              height: 32px;

              i {
                font-size: 14px;
              }
            }
          }
        }

        .project-description {
          .description-content {
            padding: 12px;
            font-size: 13px;
          }
        }
      }
    }

    .stats-cards {
      margin-bottom: 16px;

      .stat-card {
        padding: 12px;

        .stat-content .stat-number {
          font-size: 20px;
        }
      }

      &.compact .stat-card {
        padding: 12px;

        .stat-icon {
          width: 40px;
          height: 40px;

          i {
            font-size: 18px;
          }
        }

        .stat-content {
          .stat-number {
            font-size: 20px;
          }

          .stat-label {
            font-size: 12px;
          }

          .stat-status {
            font-size: 10px;
          }
        }
      }
    }

    .first-row,
    .second-row {
      margin-bottom: 16px;

      .content-card {
        height: auto !important; // 移动端取消固定高度
        margin-bottom: 16px;

        .card-header {
          padding: 16px;
          flex-direction: column;
          gap: 12px;
          align-items: flex-start;
        }

        &.quick-actions {
          .actions-grid {
            grid-template-columns: repeat(2, 1fr);
            padding: 16px;
          }
        }

        &.device-status {
          .device-stats-grid {
            grid-template-columns: repeat(2, 1fr);
            padding: 16px;
            gap: 12px;

            .device-stat-item {
              padding: 12px;

              .stat-icon {
                width: 32px;
                height: 32px;

                i {
                  font-size: 16px;
                }
              }

              .stat-content {
                .stat-number {
                  font-size: 18px;
                }

                .stat-label {
                  font-size: 11px;
                }

                .stat-status {
                  font-size: 9px;
                }
              }
            }
          }
        }

        &.alarms {
          .alarm-list {
            padding: 16px;
            max-height: none;
          }
        }

        &.operation-logs {
          .log-list {
            padding: 16px;
            max-height: none;
          }
        }
      }
    }
  }
}
</style>

