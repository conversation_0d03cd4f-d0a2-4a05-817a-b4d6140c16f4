<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--区域数据-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-input
            v-model="areaName"
            placeholder="输入关键字以查找"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-tree
            :data="meterTreeOptions"
            :props="defaultProps"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="tree"
            node-key="id"
            default-expand-all
            highlight-current
            @node-click="handleNodeClick"
          />
        </div>
      </el-col>
      <!--抄表数据-->
      <el-col :span="20" :xs="24">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
                 label-width="68px"
        >
          <el-form-item label="表具名称" prop="meterName">
            <el-input
              v-model="queryParams.meterName"
              placeholder="请输入表具名称"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="表具编号" prop="meterNumber">
            <el-input
              v-model="queryParams.meterNumber"
              placeholder="请输入表具编号"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="房号" prop="roomNumber">
            <el-input
              v-model="queryParams.roomNumber"
              placeholder="请输入房号"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="抄表方式" prop="readingWay">
            <el-select
              v-model="queryParams.readingWay"
              placeholder="抄表方式"
              clearable
            >
              <el-option
                v-for="dict in dict.type.reading_way"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="抄表时间" prop="readingTime">
            <el-date-picker
              v-model="dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期时间"
              end-placeholder="结束日期时间"
              value-format="yyyy-MM-dd HH:mm:ss"
              :default-time="['00:00:00', '23:59:59']"
              @change="handleDateRangeChange"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            <el-button
              type="warning"
              plain
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['biz:meterReading:export']"
            >导出
            </el-button>
            <el-button
              type="danger"
              plain
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['biz:meterReading:remove']"
            >删除
            </el-button>
          </el-form-item>
        </el-form>

        <el-table v-loading="loading" :data="meterReadingList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center"/>
          <el-table-column label="表具名称" align="center" prop="meterName"/>
          <el-table-column label="表具编号" align="center" prop="meterNumber"/>
          <el-table-column label="房号" align="center" prop="roomNumber"/>
          <el-table-column label="能源类型" align="center" prop="energyType">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.energy_type" :value="scope.row.energyType"/>
            </template>
          </el-table-column>
          <el-table-column label="表具读数" align="center" prop="meterRecord"/>
          <el-table-column label="表具时间" align="center" prop="meterTime"/>
          <el-table-column label="抄表方式" align="center" prop="readingWay">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.reading_way" :value="scope.row.readingWay"/>
            </template>
          </el-table-column>
          <el-table-column label="抄表时间" align="center" prop="readingTime" width="180"/>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['biz:meterReading:remove']"
              >删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {listMeterReading, delMeterReading} from '@/api/biz/meterReading'
import {meterTree} from '@/api/biz/meter'

export default {
  name: 'MeterReading',
  dicts: ['energy_type', 'reading_way'],
  data() {
    return {
      // 项目id
      projectId: localStorage.getItem('projectId'),

      // 区域名称
      areaName: undefined,
      meterTreeOptions: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },

      dateRange: [],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 抄记录表格数据
      meterReadingList: [],
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        meterId: undefined,
        meterName: undefined,
        meterNumber: undefined,
        roomNumber: undefined,
        energyType: undefined,
        meterRecord: undefined,
        meterTime: undefined,
        readingTime: undefined,
        readingWay: undefined,
        startReadingTime: undefined,
        endReadingTime: undefined,
        projectId: undefined,
      }
    }
  },
  watch: {
    // 根据名称筛选
    areaName(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.getMeterTree()
    this.getList()
  },
  methods: {
    // 时间范围选择器变化
    handleDateRangeChange(val) {
      if (val) {
        this.queryParams.startReadingTime = val[0]
        this.queryParams.endReadingTime = val[1]
      } else {
        this.queryParams.startReadingTime = undefined
        this.queryParams.endReadingTime = undefined
      }
    },
    // 获取区域表具树结构
    getMeterTree() {
      meterTree({
        projectId: this.projectId
      }).then(res => {
        this.meterTreeOptions = res.data
      })
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    // 节点单击事件
    handleNodeClick(data) {
      // 如果是区域节点,不查询,直接返回
      if (data.type === 'area') {
        return
      }
      this.queryParams.meterId = data.id
      this.handleQuery()
    },
    /** 查询抄记录列表 */
    getList() {
      this.loading = true
      this.queryParams.projectId = this.projectId
      listMeterReading(this.queryParams).then(response => {
        this.meterReadingList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.queryParams.meterId = null
      this.$refs.tree.setCurrentKey(null)
      this.queryParams.startReadingTime = undefined
      this.queryParams.endReadingTime = undefined
      this.dateRange = []
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除？').then(() => {
        this.loading = true
        return delMeterReading(ids)
      }).then(() => {
        this.loading = false
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {
      }).finally(() => {
        this.loading = false
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.queryParams.projectId = this.projectId
      this.download('biz/meterReading/export', {
        ...this.queryParams
      }, `抄表记录_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
