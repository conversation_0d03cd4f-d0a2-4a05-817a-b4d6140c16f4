<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="设备" prop="deviceId">
        <el-select
          v-model="queryParams.deviceId"
          placeholder="请选择设备"
          clearable
          filterable
          @keyup.enter.native="handleQuery"
          @change="handleQueryDeviceChange"
        >
          <el-option
            v-for="device in deviceOptions"
            :key="device.value"
            :label="device.label"
            :value="device.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="点位" prop="pointId">
        <el-select
          v-model="queryParams.pointId"
          placeholder="请选择点位"
          clearable
          filterable
          @keyup.enter.native="handleQuery"
          :disabled="!queryParams.deviceId"
        >
          <el-option
            v-for="point in queryPointOptions"
            :key="point.value"
            :label="point.label"
            :value="point.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="queryParams.remark"
          placeholder="请输入备注"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['biz:alarmRule:add']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['biz:alarmRule:edit']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['biz:alarmRule:remove']"
        >删除
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="alarmRuleList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="设备名称" align="center" prop="deviceName"/>
      <el-table-column label="点位名称" align="center" prop="pointName"/>
      <el-table-column label="报警值" align="center" prop="alarmValue"/>
      <el-table-column label="符号" align="center" prop="symbol"/>
      <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['biz:alarmRule:edit']"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['biz:alarmRule:remove']"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改报警规则对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <!-- 设备和点位选择 -->
      <el-row :gutter="20" class="device-selection-row">
        <el-col :span="24">
          <div class="section-title">
            <i class="el-icon-setting"></i>
            设备和点位选择
          </div>
          <el-card shadow="never" class="selection-card">
            <el-form ref="deviceForm" :model="form" :rules="deviceRules" label-width="80px">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="设备" prop="deviceId">
                    <el-select
                      v-model="form.deviceId"
                      placeholder="请选择设备"
                      filterable
                      clearable
                      style="width: 100%"
                      @change="handleDeviceChange"
                    >
                      <el-option
                        v-for="device in deviceOptions"
                        :key="device.value"
                        :label="device.label"
                        :value="device.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="点位" prop="pointId">
                    <el-select
                      v-model="form.pointId"
                      placeholder="请选择点位"
                      filterable
                      clearable
                      style="width: 100%"
                      :disabled="!form.deviceId"
                      @change="handlePointChange"
                    >
                      <el-option
                        v-for="point in pointOptions"
                        :key="point.value"
                        :label="point.label"
                        :value="point.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-card>
        </el-col>
      </el-row>

      <!-- 报警条件设置 -->
      <el-row :gutter="20" class="condition-row">
        <el-col :span="24">
          <div class="section-title">
            <i class="el-icon-warning"></i>
            报警条件设置
          </div>
          <el-card shadow="never" class="condition-card">
            <el-form ref="conditionForm" :model="form" :rules="conditionRules" label-width="80px">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="报警条件" prop="symbol">
                    <el-select v-model="form.symbol" placeholder="请选择条件符号" style="width: 100%">
                      <el-option label="等于 (==)" value="=="/>
                      <el-option label="不等于 (!=)" value="!="/>
                      <!-- 只有数值类型才显示大小比较符号 -->
                      <template v-if="!pointDetail || pointDetail.valueType === '1'">
                        <el-option label="大于 (>)" value=">"/>
                        <el-option label="小于 (<)" value="<"/>
                        <el-option label="大于等于 (>=)" value=">="/>
                        <el-option label="小于等于 (<=)" value="<="/>
                      </template>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="报警值" prop="alarmValue">
                    <!-- 数值类型输入 -->
                    <el-input-number
                      v-if="pointDetail && pointDetail.valueType === '1'"
                      v-model="form.alarmValue"
                      :min="pointDetail.valueMin !== null && pointDetail.valueMin !== undefined ? pointDetail.valueMin : undefined"
                      :max="pointDetail.valueMax !== null && pointDetail.valueMax !== undefined ? pointDetail.valueMax : undefined"
                      :placeholder="pointDetail.valueMin !== null && pointDetail.valueMin !== undefined && pointDetail.valueMax !== null && pointDetail.valueMax !== undefined ? `范围: ${pointDetail.valueMin} - ${pointDetail.valueMax}` : '请输入数值'"
                      style="width: 100%;"
                    />
                    <!-- 选项类型输入 -->
                    <el-select
                      v-else-if="pointDetail && pointDetail.valueType === '2'"
                      v-model="form.alarmValue"
                      placeholder="请选择报警值"
                      style="width: 100%;"
                    >
                      <el-option
                        v-for="option in parseValueOptions(pointDetail.valueOption)"
                        :key="option.value"
                        :label="option.name"
                        :value="option.value"
                      />
                    </el-select>
                    <!-- 默认文本输入 -->
                    <el-input
                      v-else
                      v-model="form.alarmValue"
                      placeholder="请输入报警值"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-card>
        </el-col>
      </el-row>

      <!-- 备注信息 -->
      <el-row :gutter="20" class="info-row">
        <el-col :span="24">
          <div class="section-title">
            <i class="el-icon-info"></i>
            备注信息
          </div>
          <el-card shadow="never" class="info-card">
            <el-form ref="infoForm" :model="form" :rules="infoRules" label-width="80px">
              <el-form-item label="备注" prop="remark">
                <el-input
                  v-model="form.remark"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入备注信息"
                />
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>
      </el-row>

      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {listAlarmRule, getAlarmRule, delAlarmRule, addAlarmRule, updateAlarmRule} from "@/api/biz/alarmRule";
import {deviceOption} from "@/api/biz/device";
import {devicePointOption, getDevicePoint} from "@/api/biz/devicePoint";

export default {
  name: "AlarmRule",
  data() {
    return {
      // 项目id
      projectId: localStorage.getItem('projectId'),
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 报警规则表格数据
      alarmRuleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 设备选项
      deviceOptions: [],
      // 点位选项
      pointOptions: [],
      // 查询条件用的点位选项
      queryPointOptions: [],
      // 点位详情
      pointDetail: null,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deviceId: undefined,
        pointId: undefined,
        alarmValue: undefined,
        symbol: undefined,
        remark: undefined,
        projectId: undefined,
      },
      // 表单参数
      form: {},
      // 设备表单校验
      deviceRules: {
        deviceId: [
          {required: true, message: "设备不能为空", trigger: "change"}
        ],
        pointId: [
          {required: true, message: "点位不能为空", trigger: "change"}
        ]
      },
      // 条件表单校验
      conditionRules: {
        symbol: [
          {required: true, message: "报警条件不能为空", trigger: "change"}
        ],
        alarmValue: [
          {required: true, message: "报警值不能为空", trigger: "blur"}
        ]
      },
      // 信息表单校验
      infoRules: {
        remark: [
          {required: true, message: "备注不能为空", trigger: "blur"}
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getDeviceOptions();
  },
  methods: {
    // 获取设备选项
    getDeviceOptions() {
      deviceOption({
        projectId: this.projectId
      }).then(response => {
        this.deviceOptions = response.data || [];
      });
    },
    // 查询条件中设备变化时，获取对应的点位选项
    handleQueryDeviceChange(deviceId) {
      this.queryParams.pointId = undefined;
      this.queryPointOptions = [];
      if (deviceId) {
        devicePointOption({deviceId: deviceId}).then(response => {
          this.queryPointOptions = response.data || [];
        }).catch(error => {
          console.error('获取查询设备点位失败:', error);
        });
      }
    },
    // 设备变化时，获取对应的点位选项
    handleDeviceChange(deviceId) {
      this.form.pointId = undefined;
      this.pointOptions = [];
      this.pointDetail = null;
      this.form.alarmValue = undefined;
      if (deviceId) {
        devicePointOption({deviceId: deviceId}).then(response => {
          this.pointOptions = response.data || [];
        }).catch(error => {
          console.error('获取设备点位失败:', error);
          this.$modal.msgError('获取设备点位失败');
        });
      }
    },
    // 点位变化时，获取点位详情
    handlePointChange(pointId) {
      this.pointDetail = null;
      this.form.alarmValue = undefined;
      if (pointId) {
        getDevicePoint(pointId).then(response => {
          this.pointDetail = response.data;
          console.log('点位详情:', this.pointDetail);

          // 如果是选项类型，且当前条件符号不是等于或不等于，则清空条件符号
          if (this.pointDetail.valueType === '2') {
            if (this.form.symbol && this.form.symbol !== '==' && this.form.symbol !== '!=') {
              this.form.symbol = undefined;
            }
          }
        }).catch(error => {
          console.error('获取点位详情失败:', error);
          this.$modal.msgError('获取点位详情失败');
        });
      }
    },
    // 解析选项值
    parseValueOptions(valueOption) {
      if (!valueOption) return [];
      try {
        return JSON.parse(valueOption);
      } catch (e) {
        console.error('解析选项值失败:', e);
        return [];
      }
    },
    /** 查询报警规则列表 */
    getList() {
      this.loading = true;
      this.queryParams.projectId = this.projectId
      listAlarmRule(this.queryParams).then(response => {
        this.alarmRuleList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        deviceId: undefined,
        pointId: undefined,
        alarmValue: undefined,
        symbol: undefined,
        remark: undefined,
        projectId: undefined,
      };
      // 清空点位详情
      this.pointDetail = null;
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.pointOptions = [];
      this.open = true;
      this.title = "添加报警规则";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.loading = true;
      this.reset();
      const id = row.id || this.ids
      getAlarmRule(id).then(response => {
        this.loading = false;
        this.form = response.data;

        // 根据已选择的设备加载对应的点位选项
        const loadPromises = [];

        if (this.form.deviceId) {
          const pointPromise = devicePointOption({deviceId: this.form.deviceId}).then(response => {
            this.pointOptions = response.data || [];
          }).catch(error => {
            console.error('获取设备点位失败:', error);
          });
          loadPromises.push(pointPromise);
        }

        // 获取点位详情
        if (this.form.pointId) {
          const pointDetailPromise = getDevicePoint(this.form.pointId).then(response => {
            this.pointDetail = response.data;
          }).catch(error => {
            console.error('获取点位详情失败:', error);
          });
          loadPromises.push(pointDetailPromise);
        }

        // 等待所有点位数据加载完成后再打开对话框
        Promise.all(loadPromises).finally(() => {
          this.open = true;
          this.title = "修改报警规则";
        });
      }).catch(error => {
        this.loading = false;
        console.error('获取报警规则详情失败:', error);
        this.$modal.msgError('获取数据失败');
      });
    },
    /** 提交按钮 */
    submitForm() {
      // 验证所有表单
      Promise.all([
        new Promise((resolve, reject) => {
          this.$refs["deviceForm"].validate(valid => {
            if (valid) resolve();
            else reject('设备选择有误');
          });
        }),
        new Promise((resolve, reject) => {
          this.$refs["conditionForm"].validate(valid => {
            if (valid) resolve();
            else reject('报警条件设置有误');
          });
        }),
        new Promise((resolve, reject) => {
          this.$refs["infoForm"].validate(valid => {
            if (valid) resolve();
            else reject('备注信息设置有误');
          });
        })
      ]).then(() => {
        this.buttonLoading = true;
        this.form.projectId = this.projectId
        if (this.form.id != null) {
          updateAlarmRule(this.form).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.open = false;
            this.getList();
          }).finally(() => {
            this.buttonLoading = false;
          });
        } else {
          addAlarmRule(this.form).then(response => {
            this.$modal.msgSuccess("新增成功");
            this.open = false;
            this.getList();
          }).finally(() => {
            this.buttonLoading = false;
          });
        }
      }).catch(error => {
        this.$modal.msgError(error || '表单验证失败');
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除报警规则编号为"' + ids + '"的数据项？').then(() => {
        this.loading = true;
        return delAlarmRule(ids);
      }).then(() => {
        this.loading = false;
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      }).finally(() => {
        this.loading = false;
      });
    }
  }
};
</script>

<style scoped>
.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.section-title i {
  margin-right: 8px;
  font-size: 18px;
  color: #409EFF;
}

/* 设备选择区域 */
.device-selection-row {
  margin-bottom: 20px;
}

.selection-card {
  border: 1px solid #E4E7ED;
  border-radius: 8px;
  background: #F8F9FA;
}

.selection-card .el-card__body {
  padding: 20px;
}

/* 条件区域 */
.condition-row {
  margin-bottom: 20px;
}

.condition-card {
  border: 1px solid #E4E7ED;
  border-radius: 8px;
  background: #F8F9FA;
}

.condition-card .el-card__body {
  padding: 20px;
}

/* 信息区域 */
.info-row {
  margin-bottom: 20px;
}

.info-card {
  border: 1px solid #E4E7ED;
  border-radius: 8px;
  background: #F8F9FA;
}

.info-card .el-card__body {
  padding: 20px;
}

.dialog-footer {
  text-align: center;
  padding: 20px 0;
  border-top: 1px solid #EBEEF5;
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .section-title {
    text-align: center;
    margin: 20px 0;
  }

  .device-selection-row .el-col,
  .condition-row .el-col,
  .info-row .el-col {
    margin-bottom: 15px;
  }
}

/* 表单项间距调整 */
.el-form-item {
  margin-bottom: 18px;
}
</style>
