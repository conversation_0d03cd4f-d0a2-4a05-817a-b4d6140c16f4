<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="报警名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入报警名称"
          clearable
          style="width: 180px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="报警对象" prop="objName">
        <el-input
          v-model="queryParams.objName"
          placeholder="请输入报警对象名称"
          clearable
          style="width: 180px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="报警点位" prop="pointName">
        <el-input
          v-model="queryParams.pointName"
          placeholder="请输入报警点位名称"
          clearable
          style="width: 180px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="报警时间" prop="alarmTimeRange">
        <el-date-picker
          v-model="queryParams.alarmTimeRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="yyyy-MM-dd HH:mm:ss"
          clearable
        />
      </el-form-item>
      <el-form-item label="是否处理" prop="handleFlag">
        <el-select v-model="queryParams.handleFlag" placeholder="请选择" style="width: 90px" clearable>
          <el-option
            v-for="dict in dict.type.yes_no"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['biz:alarmLog:remove']"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['biz:alarmLog:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="alarmLogList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="报警名称" align="center" prop="name"/>
      <el-table-column label="报警对象名称" align="center" prop="objName"/>
      <el-table-column label="报警点位名称" align="center" prop="pointName"/>
      <el-table-column label="报警值" align="center" prop="alarmValue"/>
      <el-table-column label="报警时间" align="center" prop="alarmTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.alarmTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否处理" align="center" prop="handleFlag">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.yes_no" :value="scope.row.handleFlag"/>
        </template>
      </el-table-column>
      <el-table-column label="处理时间" align="center" prop="handleTime" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.handleTime ? parseTime(scope.row.handleTime, '{y}-{m}-{d} {h}:{i}:{s}') : '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="处理备注" align="center" prop="handleRemark" :show-overflow-tooltip="true"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.handleFlag === '0'"
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleProcess(scope.row)"
            v-hasPermi="['biz:alarmLog:handle']"
          >处理
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['biz:alarmLog:remove']"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 处理报警对话框 -->
    <el-dialog title="处理报警" :visible.sync="processOpen" width="500px" append-to-body>
      <el-form ref="processForm" :model="processForm" :rules="processRules" label-width="80px">
        <el-form-item label="报警名称">
          <el-input v-model="processForm.name" readonly/>
        </el-form-item>
        <el-form-item label="报警对象">
          <el-input v-model="processForm.objName" readonly/>
        </el-form-item>
        <el-form-item label="报警点位">
          <el-input v-model="processForm.pointName" readonly/>
        </el-form-item>
        <el-form-item label="报警值">
          <el-input v-model="processForm.alarmValue" readonly/>
        </el-form-item>
        <el-form-item label="报警时间">
          <el-input v-model="processForm.alarmTime" readonly/>
        </el-form-item>
        <el-form-item label="处理时间" prop="handleTime">
          <el-date-picker
            v-model="processForm.handleTime"
            type="datetime"
            placeholder="请选择处理时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="处理说明" prop="handleRemark">
          <el-input
            v-model="processForm.handleRemark"
            type="textarea"
            :rows="4"
            placeholder="请输入处理说明"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="buttonLoading" type="primary" @click="submitProcess">确 定</el-button>
        <el-button @click="cancelProcess">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import {listAlarmLog, delAlarmLog, handle} from "@/api/biz/alarmLog";

export default {
  name: "AlarmLog",
  dicts: ['yes_no'],
  data() {
    return {
      // 项目id
      projectId: localStorage.getItem('projectId'),
      // 按钮loading
      buttonLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 报警日志表格数据
      alarmLogList: [],
      // 是否显示处理对话框
      processOpen: false,
      // 处理表单
      processForm: {
        id: undefined,
        name: undefined,
        objName: undefined,
        pointName: undefined,
        alarmValue: undefined,
        alarmTime: undefined,
        handleTime: undefined,
        handleRemark: undefined
      },
      // 处理表单校验
      processRules: {
        handleTime: [
          {required: true, message: "处理时间不能为空", trigger: "change"}
        ],
        handleRemark: [
          {required: true, message: "处理说明不能为空", trigger: "blur"}
        ]
      },

      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        alarmRuleId: undefined,
        name: undefined,
        objId: undefined,
        objName: undefined,
        pointId: undefined,
        pointName: undefined,
        alarmValue: undefined,
        alarmTimeRange: undefined,
        alarmStartTime: undefined,
        alarmEndTime: undefined,
        handleFlag: undefined,
        handleTime: undefined,
        projectId: undefined,
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询报警日志列表 */
    getList() {
      this.loading = true;
      // 处理时间范围参数
      if (this.queryParams.alarmTimeRange && this.queryParams.alarmTimeRange.length === 2) {
        this.queryParams.alarmStartTime = this.queryParams.alarmTimeRange[0];
        this.queryParams.alarmEndTime = this.queryParams.alarmTimeRange[1];
      } else {
        this.queryParams.alarmStartTime = undefined;
        this.queryParams.alarmEndTime = undefined;
      }

      this.queryParams.projectId = this.projectId
      listAlarmLog(this.queryParams).then(response => {
        this.alarmLogList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除报警日志编号为"' + ids + '"的数据项？').then(() => {
        this.loading = true;
        return delAlarmLog(ids);
      }).then(() => {
        this.loading = false;
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
      }).finally(() => {
        this.loading = false;
      });
    },
    /** 处理报警按钮操作 */
    handleProcess(row) {
      this.processForm = {
        id: row.id,
        name: row.name,
        objName: row.objName,
        pointName: row.pointName,
        alarmValue: row.alarmValue,
        alarmTime: this.parseTime(row.alarmTime, '{y}-{m}-{d} {h}:{i}:{s}'),
        handleTime: this.parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}'), // 默认为当前时间
        handleRemark: undefined
      };
      this.processOpen = true;
    },
    /** 提交处理 */
    submitProcess() {
      this.$refs["processForm"].validate(valid => {
        if (valid) {
          this.buttonLoading = true;
          const data = {
            id: this.processForm.id,
            handleRemark: this.processForm.handleRemark,
            handleTime: this.processForm.handleTime // 用户选择的处理时间
          };
          handle(data).then(response => {
            this.$modal.msgSuccess("处理成功");
            this.processOpen = false;
            this.getList();
          }).finally(() => {
            this.buttonLoading = false;
          });
        }
      });
    },
    /** 取消处理 */
    cancelProcess() {
      this.processOpen = false;
      this.resetProcessForm();
    },
    /** 重置处理表单 */
    resetProcessForm() {
      this.processForm = {
        id: undefined,
        name: undefined,
        objName: undefined,
        pointName: undefined,
        alarmValue: undefined,
        alarmTime: undefined,
        handleTime: undefined,
        handleRemark: undefined
      };
      this.resetForm("processForm");
    },
    /** 导出按钮操作 */
    handleExport() {
      this.queryParams.projectId = this.projectId
      this.download('biz/alarmLog/export', {
        ...this.queryParams
      }, `alarmLog_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
