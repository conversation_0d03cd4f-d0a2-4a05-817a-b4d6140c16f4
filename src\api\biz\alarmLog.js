import request from '@/utils/request'

// 查询报警日志列表
export function listAlarmLog(query) {
  return request({
    url: '/biz/alarmLog/list',
    method: 'get',
    params: query
  })
}

// 查询报警日志详细
export function getAlarmLog(id) {
  return request({
    url: '/biz/alarmLog/' + id,
    method: 'get'
  })
}

// 新增报警日志
export function addAlarmLog(data) {
  return request({
    url: '/biz/alarmLog',
    method: 'post',
    data: data
  })
}

// 修改报警日志
export function updateAlarmLog(data) {
  return request({
    url: '/biz/alarmLog',
    method: 'put',
    data: data
  })
}

// 删除报警日志
export function delAlarmLog(id) {
  return request({
    url: '/biz/alarmLog/' + id,
    method: 'delete'
  })
}

// 处理
export function handle(data) {
  return request({
    url: '/biz/alarmLog/handle',
    method: 'post',
    data: data
  })
}
